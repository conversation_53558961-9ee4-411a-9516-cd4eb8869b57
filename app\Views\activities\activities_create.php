<?= $this->extend('templates/admin_template') ?>

<?= $this->section('content') ?>
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h2 class="text-primary-custom mb-2">
                                <i class="bi bi-plus-circle me-2"></i>Create New Activity
                            </h2>
                            <p class="text-muted mb-0">Add a new activity to your workplan</p>
                        </div>
                        <div>
                            <a href="<?= base_url('admin/activities') ?>" class="btn btn-secondary">
                                <i class="bi bi-arrow-left me-2"></i>Back to Activities
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Alerts -->
    <?php if (session()->getFlashdata('error')): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="bi bi-exclamation-triangle me-2"></i>
            <?= session()->getFlashdata('error') ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if (session()->getFlashdata('errors')): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="bi bi-exclamation-triangle me-2"></i>
            <strong>Please fix the following errors:</strong>
            <ul class="mb-0 mt-2">
                <?php foreach (session()->getFlashdata('errors') as $error): ?>
                    <li><?= esc($error) ?></li>
                <?php endforeach; ?>
            </ul>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- Create Form -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-form me-2"></i>Activity Information
                    </h5>
                </div>
                <div class="card-body">
                    <?= form_open('admin/activities/create', ['class' => 'needs-validation', 'novalidate' => true]) ?>
                        <div class="row">
                            <!-- Workplan Selection -->
                            <div class="col-md-6 mb-3">
                                <label for="workplan_id" class="form-label">
                                    <i class="bi bi-calendar-check me-1"></i>Workplan <span class="text-danger">*</span>
                                </label>
                                <select class="form-select" id="workplan_id" name="workplan_id" required>
                                    <option value="">Select a workplan...</option>
                                    <?php foreach ($workplans as $workplan): ?>
                                        <option value="<?= $workplan['id'] ?>" 
                                                <?= old('workplan_id') == $workplan['id'] ? 'selected' : '' ?>
                                                data-date-from="<?= $workplan['date_from'] ?>"
                                                data-date-to="<?= $workplan['date_to'] ?>">
                                            <?= esc($workplan['title']) ?> 
                                            (<?= date('M d, Y', strtotime($workplan['date_from'])) ?> - <?= date('M d, Y', strtotime($workplan['date_to'])) ?>)
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                                <div class="invalid-feedback">
                                    Please select a workplan.
                                </div>
                                <small class="form-text text-muted">
                                    Activity dates must be within the selected workplan period.
                                </small>
                            </div>

                            <!-- Activity Type -->
                            <div class="col-md-6 mb-3">
                                <label for="activity_type" class="form-label">
                                    <i class="bi bi-tag me-1"></i>Activity Type <span class="text-danger">*</span>
                                </label>
                                <select class="form-select" id="activity_type" name="activity_type" required>
                                    <option value="">Select activity type...</option>
                                    <?php foreach ($activity_types as $value => $label): ?>
                                        <option value="<?= $value ?>" <?= old('activity_type') == $value ? 'selected' : '' ?>>
                                            <?= esc($label) ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                                <div class="invalid-feedback">
                                    Please select an activity type.
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <!-- Activity Name -->
                            <div class="col-12 mb-3">
                                <label for="activity_name" class="form-label">
                                    <i class="bi bi-activity me-1"></i>Activity Name <span class="text-danger">*</span>
                                </label>
                                <input type="text" 
                                       class="form-control" 
                                       id="activity_name" 
                                       name="activity_name" 
                                       value="<?= old('activity_name') ?>"
                                       placeholder="Enter activity name (e.g., Maprik Inspection)"
                                       maxlength="200"
                                       required>
                                <div class="invalid-feedback">
                                    Please provide a valid activity name.
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <!-- Date From -->
                            <div class="col-md-6 mb-3">
                                <label for="date_from" class="form-label">
                                    <i class="bi bi-calendar-event me-1"></i>Start Date <span class="text-danger">*</span>
                                </label>
                                <input type="date" 
                                       class="form-control" 
                                       id="date_from" 
                                       name="date_from" 
                                       value="<?= old('date_from') ?>"
                                       required>
                                <div class="invalid-feedback">
                                    Please provide a valid start date.
                                </div>
                            </div>

                            <!-- Date To -->
                            <div class="col-md-6 mb-3">
                                <label for="date_to" class="form-label">
                                    <i class="bi bi-calendar-event me-1"></i>End Date <span class="text-danger">*</span>
                                </label>
                                <input type="date" 
                                       class="form-control" 
                                       id="date_to" 
                                       name="date_to" 
                                       value="<?= old('date_to') ?>"
                                       required>
                                <div class="invalid-feedback">
                                    Please provide a valid end date.
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <!-- Remarks -->
                            <div class="col-12 mb-3">
                                <label for="remarks" class="form-label">
                                    <i class="bi bi-chat-text me-1"></i>Remarks
                                </label>
                                <textarea class="form-control" 
                                          id="remarks" 
                                          name="remarks" 
                                          rows="4"
                                          placeholder="Enter any additional remarks or notes about this activity..."><?= old('remarks') ?></textarea>
                                <small class="form-text text-muted">
                                    Optional: Add any additional information about this activity.
                                </small>
                            </div>
                        </div>

                        <!-- Submit Buttons -->
                        <div class="row">
                            <div class="col-12">
                                <div class="d-flex justify-content-end gap-2">
                                    <a href="<?= base_url('admin/activities') ?>" class="btn btn-secondary">
                                        <i class="bi bi-x-circle me-2"></i>Cancel
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="bi bi-check-circle me-2"></i>Create Activity
                                    </button>
                                </div>
                            </div>
                        </div>
                    <?= form_close() ?>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Form validation
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();

// Date validation
document.addEventListener('DOMContentLoaded', function() {
    const workplanSelect = document.getElementById('workplan_id');
    const dateFromInput = document.getElementById('date_from');
    const dateToInput = document.getElementById('date_to');
    
    // Update date constraints when workplan changes
    workplanSelect.addEventListener('change', function() {
        const selectedOption = this.options[this.selectedIndex];
        if (selectedOption.value) {
            const workplanDateFrom = selectedOption.getAttribute('data-date-from');
            const workplanDateTo = selectedOption.getAttribute('data-date-to');
            
            dateFromInput.min = workplanDateFrom;
            dateFromInput.max = workplanDateTo;
            dateToInput.min = workplanDateFrom;
            dateToInput.max = workplanDateTo;
            
            // Clear dates if they're outside the workplan period
            if (dateFromInput.value && (dateFromInput.value < workplanDateFrom || dateFromInput.value > workplanDateTo)) {
                dateFromInput.value = '';
            }
            if (dateToInput.value && (dateToInput.value < workplanDateFrom || dateToInput.value > workplanDateTo)) {
                dateToInput.value = '';
            }
        } else {
            dateFromInput.removeAttribute('min');
            dateFromInput.removeAttribute('max');
            dateToInput.removeAttribute('min');
            dateToInput.removeAttribute('max');
        }
    });
    
    // Validate end date is after start date
    dateFromInput.addEventListener('change', function() {
        if (this.value) {
            dateToInput.min = this.value;
            if (dateToInput.value && dateToInput.value <= this.value) {
                dateToInput.value = '';
            }
        }
    });
    
    // Trigger workplan change event if there's a pre-selected value
    if (workplanSelect.value) {
        workplanSelect.dispatchEvent(new Event('change'));
    }
});
</script>
<?= $this->endSection() ?>
