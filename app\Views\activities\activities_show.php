<?= $this->extend('templates/admin_template') ?>

<?= $this->section('content') ?>
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h2 class="text-primary-custom mb-2">
                                <i class="bi bi-activity me-2"></i>Activity Details
                            </h2>
                            <p class="text-muted mb-0">View detailed information about this activity</p>
                        </div>
                        <div>
                            <a href="<?= base_url('admin/activities') ?>" class="btn btn-secondary me-2">
                                <i class="bi bi-arrow-left me-2"></i>Back to Activities
                            </a>
                            <a href="<?= base_url('admin/activities/' . $activity['id'] . '/edit') ?>" class="btn btn-warning">
                                <i class="bi bi-pencil me-2"></i>Edit Activity
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Activity Details -->
    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-info-circle me-2"></i>Activity Information
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted">Activity Name</label>
                            <p class="h5 text-dark"><?= esc($activity['activity_name']) ?></p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted">Activity Type</label>
                            <p>
                                <?php
                                $typeClass = '';
                                $typeIcon = '';
                                switch ($activity['activity_type']) {
                                    case 'price_collection':
                                        $typeClass = 'bg-primary';
                                        $typeIcon = 'bi-currency-dollar';
                                        break;
                                    case 'market_survey':
                                        $typeClass = 'bg-info';
                                        $typeIcon = 'bi-graph-up';
                                        break;
                                    case 'business_registration':
                                        $typeClass = 'bg-success';
                                        $typeIcon = 'bi-building';
                                        break;
                                    case 'data_verification':
                                        $typeClass = 'bg-warning';
                                        $typeIcon = 'bi-check-square';
                                        break;
                                    case 'training':
                                        $typeClass = 'bg-secondary';
                                        $typeIcon = 'bi-book';
                                        break;
                                    default:
                                        $typeClass = 'bg-dark';
                                        $typeIcon = 'bi-gear';
                                }
                                ?>
                                <span class="badge <?= $typeClass ?> fs-6">
                                    <i class="<?= $typeIcon ?> me-1"></i><?= ucwords(str_replace('_', ' ', $activity['activity_type'])) ?>
                                </span>
                            </p>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted">Workplan</label>
                            <p class="text-dark">
                                <i class="bi bi-calendar-check me-1"></i>
                                <?= esc($activity['workplan_title']) ?>
                            </p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted">Status</label>
                            <p>
                                <?php
                                $statusClass = '';
                                $statusIcon = '';
                                switch ($activity['status']) {
                                    case 'active':
                                        $statusClass = 'bg-success';
                                        $statusIcon = 'bi-check-circle';
                                        break;
                                    case 'submitted':
                                        $statusClass = 'bg-info';
                                        $statusIcon = 'bi-upload';
                                        break;
                                    case 'approved':
                                        $statusClass = 'bg-primary';
                                        $statusIcon = 'bi-check-circle-fill';
                                        break;
                                    case 'redo':
                                        $statusClass = 'bg-warning';
                                        $statusIcon = 'bi-arrow-clockwise';
                                        break;
                                    case 'cancelled':
                                        $statusClass = 'bg-danger';
                                        $statusIcon = 'bi-x-circle';
                                        break;
                                    default:
                                        $statusClass = 'bg-secondary';
                                        $statusIcon = 'bi-question-circle';
                                }
                                ?>
                                <span class="badge <?= $statusClass ?> fs-6">
                                    <i class="<?= $statusIcon ?> me-1"></i><?= ucfirst($activity['status']) ?>
                                </span>
                            </p>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted">Start Date</label>
                            <p class="text-dark">
                                <i class="bi bi-calendar-event me-1"></i>
                                <?= date('F d, Y', strtotime($activity['date_from'])) ?>
                            </p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted">End Date</label>
                            <p class="text-dark">
                                <i class="bi bi-calendar-event me-1"></i>
                                <?= date('F d, Y', strtotime($activity['date_to'])) ?>
                            </p>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-12 mb-3">
                            <label class="form-label text-muted">Duration</label>
                            <p class="text-dark">
                                <i class="bi bi-clock me-1"></i>
                                <?php
                                $start = new DateTime($activity['date_from']);
                                $end = new DateTime($activity['date_to']);
                                $interval = $start->diff($end);
                                echo $interval->days + 1; // +1 to include both start and end dates
                                ?> day<?= ($interval->days + 1) > 1 ? 's' : '' ?>
                            </p>
                        </div>
                    </div>

                    <?php if (!empty($activity['remarks'])): ?>
                        <div class="row">
                            <div class="col-12 mb-3">
                                <label class="form-label text-muted">Remarks</label>
                                <div class="border rounded p-3 bg-light">
                                    <?= nl2br(esc($activity['remarks'])) ?>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>

                    <?php if (!empty($activity['status_remarks'])): ?>
                        <div class="row">
                            <div class="col-12 mb-3">
                                <label class="form-label text-muted">Status Remarks</label>
                                <div class="border rounded p-3 bg-light">
                                    <?= nl2br(esc($activity['status_remarks'])) ?>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <!-- Status Information -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-info-square me-2"></i>Status Information
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (!empty($activity['status_by_name'])): ?>
                        <div class="mb-3">
                            <label class="form-label text-muted">Status Updated By</label>
                            <p class="text-dark">
                                <i class="bi bi-person me-1"></i>
                                <?= esc($activity['status_by_name']) ?>
                            </p>
                        </div>
                    <?php endif; ?>

                    <?php if (!empty($activity['status_at'])): ?>
                        <div class="mb-3">
                            <label class="form-label text-muted">Status Updated At</label>
                            <p class="text-dark">
                                <i class="bi bi-clock me-1"></i>
                                <?= date('F d, Y \a\t g:i A', strtotime($activity['status_at'])) ?>
                            </p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Creation Information -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-clock-history me-2"></i>Creation Information
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="form-label text-muted">Created By</label>
                        <p class="text-dark">
                            <i class="bi bi-person me-1"></i>
                            <?= esc($activity['created_by_name']) ?>
                        </p>
                    </div>

                    <div class="mb-3">
                        <label class="form-label text-muted">Created At</label>
                        <p class="text-dark">
                            <i class="bi bi-calendar-plus me-1"></i>
                            <?= date('F d, Y \a\t g:i A', strtotime($activity['created_at'])) ?>
                        </p>
                    </div>

                    <?php if (!empty($activity['updated_at']) && $activity['updated_at'] !== $activity['created_at']): ?>
                        <div class="mb-0">
                            <label class="form-label text-muted">Last Updated</label>
                            <p class="text-dark">
                                <i class="bi bi-pencil-square me-1"></i>
                                <?= date('F d, Y \a\t g:i A', strtotime($activity['updated_at'])) ?>
                            </p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>
