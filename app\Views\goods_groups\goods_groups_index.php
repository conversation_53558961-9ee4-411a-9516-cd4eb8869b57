<?= $this->extend('templates/admin_template') ?>

<?= $this->section('content') ?>
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card card-dark">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h2 class="text-primary-custom mb-2">
                                <i class="bi bi-collection me-2"></i>Goods Groups Management
                            </h2>
                            <p class="text-muted mb-0">Manage goods categories and groups</p>
                        </div>
                        <div>
                            <a href="<?= base_url('admin/goods-groups/new') ?>" class="btn btn-primary">
                                <i class="bi bi-plus-circle me-2"></i>Add New Group
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Navigation -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card card-dark">
                <div class="card-header">
                    <h6 class="card-title text-light mb-0">
                        <i class="bi bi-compass me-2"></i>Quick Navigation
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row g-2">
                        <div class="col-md-4">
                            <a href="<?= base_url('admin/goods-brands') ?>" class="btn btn-outline-info w-100">
                                <i class="bi bi-tags me-2"></i>Manage Brands
                            </a>
                        </div>
                        <div class="col-md-4">
                            <a href="<?= base_url('admin/goods-items') ?>" class="btn btn-outline-warning w-100">
                                <i class="bi bi-box me-2"></i>Manage Items
                            </a>
                        </div>
                        <div class="col-md-4">
                            <a href="<?= base_url('admin/dashboard') ?>" class="btn btn-outline-secondary w-100">
                                <i class="bi bi-speedometer2 me-2"></i>Dashboard
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Groups Table -->
    <div class="row">
        <div class="col-12">
            <div class="card card-dark">
                <div class="card-header">
                    <h5 class="card-title text-light mb-0">
                        <i class="bi bi-list me-2"></i>Goods Groups List
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (empty($groups)): ?>
                        <div class="text-center py-5">
                            <i class="bi bi-collection text-muted" style="font-size: 4rem;"></i>
                            <h4 class="text-muted mt-3">No Goods Groups Found</h4>
                            <p class="text-muted">Start by adding your first goods group.</p>
                            <a href="<?= base_url('admin/goods-groups/new') ?>" class="btn btn-primary">
                                <i class="bi bi-plus-circle me-2"></i>Add Goods Group
                            </a>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-dark">
                                    <tr>
                                        <th>#</th>
                                        <th>Group Name</th>
                                        <th>Description</th>
                                        <th>Status</th>
                                        <th>Brands Count</th>
                                        <th>Created</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody class="table-light">
                                    <?php foreach ($groups as $index => $group): ?>
                                        <tr>
                                            <td><?= $index + 1 ?></td>
                                            <td>
                                                <strong class="text-primary-custom"><?= esc($group['group_name']) ?></strong>
                                            </td>
                                            <td>
                                                <?php if (!empty($group['description'])): ?>
                                                    <?= esc(substr($group['description'], 0, 100)) ?>
                                                    <?= strlen($group['description']) > 100 ? '...' : '' ?>
                                                <?php else: ?>
                                                    <span class="text-muted">No description</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if ($group['status'] === 'active'): ?>
                                                    <span class="badge bg-success">
                                                        <i class="bi bi-check-circle me-1"></i>Active
                                                    </span>
                                                <?php else: ?>
                                                    <span class="badge bg-secondary">
                                                        <i class="bi bi-pause-circle me-1"></i>Inactive
                                                    </span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <span class="badge bg-info">
                                                    <?= $group['brand_count'] ?? 0 ?> brands
                                                </span>
                                            </td>
                                            <td>
                                                <small class="text-muted">
                                                    <?= date('M d, Y', strtotime($group['created_at'])) ?>
                                                </small>
                                            </td>
                                            <td>
                                                <div class="d-flex gap-2">
                                                    <!-- Navigation Button (Primary Action) -->
                                                    <a href="<?= base_url('admin/goods-brands?group_id=' . $group['id']) ?>"
                                                       class="btn btn-sm btn-primary">
                                                        <i class="bi bi-tags me-1"></i>View Brands
                                                    </a>

                                                    <!-- Record Modification Buttons -->
                                                    <div class="btn-group" role="group">
                                                        <a href="<?= base_url('admin/goods-groups/' . $group['id']) ?>"
                                                           class="btn btn-sm btn-outline-info" title="View">
                                                            <i class="bi bi-eye"></i>
                                                        </a>
                                                        <a href="<?= base_url('admin/goods-groups/' . $group['id'] . '/edit') ?>"
                                                           class="btn btn-sm btn-outline-warning" title="Edit">
                                                            <i class="bi bi-pencil"></i>
                                                        </a>
                                                        <?php if (($group['brand_count'] ?? 0) == 0): ?>
                                                            <form method="post" action="<?= base_url('admin/goods-groups/' . $group['id'] . '/delete') ?>"
                                                                  class="d-inline" onsubmit="return confirm('Are you sure you want to delete this goods group?')">
                                                                <button type="submit" class="btn btn-sm btn-outline-danger" title="Delete">
                                                                    <i class="bi bi-trash"></i>
                                                                </button>
                                                            </form>
                                                        <?php else: ?>
                                                            <button type="button" class="btn btn-sm btn-outline-danger"
                                                                    title="Cannot delete - has brands" disabled>
                                                                <i class="bi bi-trash"></i>
                                                            </button>
                                                        <?php endif; ?>
                                                    </div>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>
