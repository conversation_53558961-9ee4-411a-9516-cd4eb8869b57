<?= $this->extend('templates/admin_template') ?>

<?= $this->section('content') ?>
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card card-dark">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h2 class="text-primary-custom mb-2">
                                <i class="bi bi-plus-circle me-2"></i>Add New Goods Group
                            </h2>
                            <p class="text-muted mb-0">Create a new goods category or group</p>
                        </div>
                        <div>
                            <a href="<?= base_url('admin/goods-groups') ?>" class="btn btn-secondary">
                                <i class="bi bi-arrow-left me-2"></i>Back to List
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Create Form -->
    <div class="row">
        <div class="col-lg-8 col-md-10 mx-auto">
            <div class="card card-dark">
                <div class="card-header">
                    <h5 class="card-title text-light mb-0">
                        <i class="bi bi-form me-2"></i>Goods Group Information
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (session()->getFlashdata('errors')): ?>
                        <div class="alert alert-danger">
                            <h6><i class="bi bi-exclamation-triangle me-2"></i>Please fix the following errors:</h6>
                            <ul class="mb-0">
                                <?php foreach (session()->getFlashdata('errors') as $error): ?>
                                    <li><?= esc($error) ?></li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    <?php endif; ?>

                    <form method="post" action="<?= base_url('admin/goods-groups/create') ?>">
                        <?= csrf_field() ?>
                        
                        <div class="row">
                            <div class="col-md-12">
                                <div class="mb-3">
                                    <label for="group_name" class="form-label text-light">
                                        Group Name <span class="text-danger">*</span>
                                    </label>
                                    <input type="text" 
                                           class="form-control form-control-dark" 
                                           id="group_name" 
                                           name="group_name" 
                                           value="<?= old('group_name') ?>" 
                                           placeholder="e.g., Rice, Sugar, Oil"
                                           required>
                                    <div class="form-text text-muted">
                                        Enter a unique name for this goods group (max 100 characters)
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-12">
                                <div class="mb-3">
                                    <label for="description" class="form-label text-light">
                                        Description
                                    </label>
                                    <textarea class="form-control form-control-dark" 
                                              id="description" 
                                              name="description" 
                                              rows="4" 
                                              placeholder="Optional description of this goods group"><?= old('description') ?></textarea>
                                    <div class="form-text text-muted">
                                        Provide additional details about this goods group (optional)
                                    </div>
                                </div>
                            </div>
                        </div>



                        <div class="row">
                            <div class="col-12">
                                <hr class="border-secondary">
                                <div class="d-flex justify-content-between">
                                    <a href="<?= base_url('admin/goods-groups') ?>" class="btn btn-secondary">
                                        <i class="bi bi-x-circle me-2"></i>Cancel
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="bi bi-check-circle me-2"></i>Create Goods Group
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-focus on group name field
    document.getElementById('group_name').focus();
    
    // Character counter for group name
    const groupNameInput = document.getElementById('group_name');
    const maxLength = 100;
    
    groupNameInput.addEventListener('input', function() {
        const remaining = maxLength - this.value.length;
        const helpText = this.parentNode.querySelector('.form-text');
        
        if (remaining < 20) {
            helpText.innerHTML = `Enter a unique name for this goods group (${remaining} characters remaining)`;
            helpText.className = remaining < 10 ? 'form-text text-warning' : 'form-text text-info';
        } else {
            helpText.innerHTML = 'Enter a unique name for this goods group (max 100 characters)';
            helpText.className = 'form-text text-muted';
        }
    });
});
</script>
<?= $this->endSection() ?>
