<?php

namespace App\Controllers;

use App\Models\ActivityModel;
use App\Models\WorkplanModel;
use App\Models\UserModel;
use App\Models\DakoiiOrgModel;
use CodeIgniter\Controller;

class Activities extends Controller
{
    protected $activityModel;
    protected $workplanModel;
    protected $userModel;
    protected $dakoiiOrgModel;
    
    public function __construct()
    {
        $this->activityModel = new ActivityModel();
        $this->workplanModel = new WorkplanModel();
        $this->userModel = new UserModel();
        $this->dakoiiOrgModel = new DakoiiOrgModel();
        helper('form');
    }

    /**
     * Check if user is logged in
     */
    private function checkAuth()
    {
        if (!session()->get('admin_logged_in')) {
            return redirect()->to('admin')->with('error', 'Please login to access this page.');
        }
        return null;
    }
    
    /**
     * Display list of activities
     */
    public function index()
    {
        $authCheck = $this->checkAuth();
        if ($authCheck) return $authCheck;

        $orgId = session()->get('admin_org_id');

        // Check for workplan filter
        $workplanId = $this->request->getGet('workplan_id');
        $selectedWorkplan = null;

        $query = $this->activityModel->select('activities.*, workplans.title as workplan_title, users.name as status_by_name')
                                   ->join('workplans', 'workplans.id = activities.workplan_id', 'left')
                                   ->join('users', 'users.id = activities.status_by', 'left')
                                   ->where('activities.org_id', $orgId)
                                   ->where('activities.is_deleted', false);

        if ($workplanId) {
            $selectedWorkplan = $this->workplanModel->find($workplanId);
            if (!$selectedWorkplan || $selectedWorkplan['org_id'] != $orgId) {
                return redirect()->to('admin/activities')->with('error', 'Invalid workplan selected.');
            }
            $query->where('activities.workplan_id', $workplanId);
        }

        $activities = $query->orderBy('activities.date_from', 'DESC')->findAll();

        $data = [
            'title' => 'Activities Management',
            'activities' => $activities,
            'selected_workplan' => $selectedWorkplan,
            'workplan_id' => $workplanId
        ];

        return view('activities/activities_index', $data);
    }
    
    /**
     * Show create form
     */
    public function new()
    {
        $authCheck = $this->checkAuth();
        if ($authCheck) return $authCheck;

        $orgId = session()->get('admin_org_id');

        // Check for pre-selected workplan
        $workplanId = $this->request->getGet('workplan_id');
        $selectedWorkplan = null;

        if ($workplanId) {
            $selectedWorkplan = $this->workplanModel->find($workplanId);
            if (!$selectedWorkplan || $selectedWorkplan['org_id'] != $orgId) {
                return redirect()->to('admin/activities/new')->with('error', 'Invalid workplan selected.');
            }
        }

        // Get active workplans for dropdown
        $workplans = $this->workplanModel->where('org_id', $orgId)
                                        ->where('status', 'active')
                                        ->where('is_deleted', false)
                                        ->orderBy('title', 'ASC')
                                        ->findAll();

        $data = [
            'title' => 'Create New Activity',
            'workplans' => $workplans,
            'selected_workplan' => $selectedWorkplan,
            'workplan_id' => $workplanId,
            'activity_types' => [
                'price_collection' => 'Price Collection',
                'market_survey' => 'Market Survey',
                'business_registration' => 'Business Registration',
                'data_verification' => 'Data Verification',
                'training' => 'Training',
                'other' => 'Other'
            ]
        ];

        return view('activities/activities_create', $data);
    }
    
    /**
     * Handle create form submission
     */
    public function create()
    {
        $authCheck = $this->checkAuth();
        if ($authCheck) return $authCheck;
        
        $rules = [
            'workplan_id' => 'required|integer',
            'activity_type' => 'required|max_length[50]',
            'activity_name' => 'required|max_length[200]',
            'date_from' => 'required|valid_date',
            'date_to' => 'required|valid_date',
            'remarks' => 'permit_empty'
        ];
        
        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }
        
        // Validate date range
        $dateFrom = $this->request->getPost('date_from');
        $dateTo = $this->request->getPost('date_to');
        
        if (strtotime($dateFrom) >= strtotime($dateTo)) {
            return redirect()->back()->withInput()->with('error', 'End date must be after start date.');
        }
        
        // Validate workplan belongs to organization
        $orgId = session()->get('admin_org_id');
        $workplan = $this->workplanModel->where('id', $this->request->getPost('workplan_id'))
                                      ->where('org_id', $orgId)
                                      ->where('is_deleted', false)
                                      ->first();
        
        if (!$workplan) {
            return redirect()->back()->withInput()->with('error', 'Invalid workplan selection.');
        }
        
        // Validate activity dates are within workplan period
        if (strtotime($dateFrom) < strtotime($workplan['date_from']) || 
            strtotime($dateTo) > strtotime($workplan['date_to'])) {
            return redirect()->back()->withInput()->with('error', 'Activity dates must be within the workplan period (' . 
                date('M d, Y', strtotime($workplan['date_from'])) . ' - ' . 
                date('M d, Y', strtotime($workplan['date_to'])) . ').');
        }
        
        $activityData = [
            'org_id' => $orgId,
            'workplan_id' => $this->request->getPost('workplan_id'),
            'activity_type' => $this->request->getPost('activity_type'),
            'activity_name' => $this->request->getPost('activity_name'),
            'date_from' => $dateFrom,
            'date_to' => $dateTo,
            'remarks' => $this->request->getPost('remarks'),
            'status' => 'active', // Automatically set to active
            'status_by' => session()->get('admin_user_id'),
            'status_at' => date('Y-m-d H:i:s'),
            'created_by' => session()->get('admin_user_id')
        ];
        
        if ($this->activityModel->insert($activityData)) {
            return redirect()->to('admin/activities')->with('success', 'Activity created successfully.');
        } else {
            return redirect()->back()->withInput()->with('error', 'Failed to create activity.');
        }
    }
    
    /**
     * Show single activity
     */
    public function show($id)
    {
        $authCheck = $this->checkAuth();
        if ($authCheck) return $authCheck;
        
        $orgId = session()->get('admin_org_id');
        $activity = $this->activityModel->select('activities.*, workplans.title as workplan_title, 
                                                users.name as status_by_name, creator.name as created_by_name')
                                      ->join('workplans', 'workplans.id = activities.workplan_id', 'left')
                                      ->join('users', 'users.id = activities.status_by', 'left')
                                      ->join('users as creator', 'creator.id = activities.created_by', 'left')
                                      ->where('activities.id', $id)
                                      ->where('activities.org_id', $orgId)
                                      ->where('activities.is_deleted', false)
                                      ->first();
        
        if (!$activity) {
            return redirect()->to('admin/activities')->with('error', 'Activity not found.');
        }
        
        $data = [
            'title' => 'Activity Details',
            'activity' => $activity
        ];
        
        return view('activities/activities_show', $data);
    }
    
    /**
     * Show edit form
     */
    public function edit($id)
    {
        $authCheck = $this->checkAuth();
        if ($authCheck) return $authCheck;
        
        $orgId = session()->get('admin_org_id');
        $activity = $this->activityModel->where('id', $id)
                                      ->where('org_id', $orgId)
                                      ->where('is_deleted', false)
                                      ->first();
        
        if (!$activity) {
            return redirect()->to('admin/activities')->with('error', 'Activity not found.');
        }
        
        // Get active workplans for dropdown
        $workplans = $this->workplanModel->where('org_id', $orgId)
                                        ->where('status', 'active')
                                        ->where('is_deleted', false)
                                        ->orderBy('title', 'ASC')
                                        ->findAll();
        
        $data = [
            'title' => 'Edit Activity',
            'activity' => $activity,
            'workplans' => $workplans,
            'activity_types' => [
                'price_collection' => 'Price Collection',
                'market_survey' => 'Market Survey',
                'business_registration' => 'Business Registration',
                'data_verification' => 'Data Verification',
                'training' => 'Training',
                'other' => 'Other'
            ]
        ];
        
        return view('activities/activities_edit', $data);
    }
    
    /**
     * Handle update form submission
     */
    public function update($id)
    {
        $authCheck = $this->checkAuth();
        if ($authCheck) return $authCheck;
        
        $orgId = session()->get('admin_org_id');
        $activity = $this->activityModel->where('id', $id)
                                      ->where('org_id', $orgId)
                                      ->where('is_deleted', false)
                                      ->first();
        
        if (!$activity) {
            return redirect()->to('admin/activities')->with('error', 'Activity not found.');
        }
        
        $rules = [
            'workplan_id' => 'required|integer',
            'activity_type' => 'required|max_length[50]',
            'activity_name' => 'required|max_length[200]',
            'date_from' => 'required|valid_date',
            'date_to' => 'required|valid_date',
            'status' => 'required|in_list[active,submitted,approved,redo,cancelled]',
            'remarks' => 'permit_empty',
            'status_remarks' => 'permit_empty'
        ];
        
        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }
        
        // Validate date range
        $dateFrom = $this->request->getPost('date_from');
        $dateTo = $this->request->getPost('date_to');
        
        if (strtotime($dateFrom) >= strtotime($dateTo)) {
            return redirect()->back()->withInput()->with('error', 'End date must be after start date.');
        }
        
        // Validate workplan belongs to organization
        $workplan = $this->workplanModel->where('id', $this->request->getPost('workplan_id'))
                                      ->where('org_id', $orgId)
                                      ->where('is_deleted', false)
                                      ->first();
        
        if (!$workplan) {
            return redirect()->back()->withInput()->with('error', 'Invalid workplan selection.');
        }
        
        // Validate activity dates are within workplan period
        if (strtotime($dateFrom) < strtotime($workplan['date_from']) || 
            strtotime($dateTo) > strtotime($workplan['date_to'])) {
            return redirect()->back()->withInput()->with('error', 'Activity dates must be within the workplan period (' . 
                date('M d, Y', strtotime($workplan['date_from'])) . ' - ' . 
                date('M d, Y', strtotime($workplan['date_to'])) . ').');
        }
        
        $updateData = [
            'workplan_id' => $this->request->getPost('workplan_id'),
            'activity_type' => $this->request->getPost('activity_type'),
            'activity_name' => $this->request->getPost('activity_name'),
            'date_from' => $dateFrom,
            'date_to' => $dateTo,
            'remarks' => $this->request->getPost('remarks'),
            'status' => $this->request->getPost('status'),
            'status_remarks' => $this->request->getPost('status_remarks'),
            'updated_by' => session()->get('admin_user_id')
        ];
        
        // Update status tracking if status changed
        if ($updateData['status'] !== $activity['status']) {
            $updateData['status_by'] = session()->get('admin_user_id');
            $updateData['status_at'] = date('Y-m-d H:i:s');
        }
        
        if ($this->activityModel->update($id, $updateData)) {
            return redirect()->to('admin/activities')->with('success', 'Activity updated successfully.');
        } else {
            return redirect()->back()->withInput()->with('error', 'Failed to update activity.');
        }
    }
    
    /**
     * Handle delete
     */
    public function delete($id)
    {
        $authCheck = $this->checkAuth();
        if ($authCheck) return $authCheck;
        
        $orgId = session()->get('admin_org_id');
        $activity = $this->activityModel->where('id', $id)
                                      ->where('org_id', $orgId)
                                      ->where('is_deleted', false)
                                      ->first();
        
        if (!$activity) {
            return redirect()->to('admin/activities')->with('error', 'Activity not found.');
        }
        
        $deleteData = [
            'is_deleted' => true,
            'deleted_by' => session()->get('admin_user_id'),
            'deleted_at' => date('Y-m-d H:i:s')
        ];
        
        if ($this->activityModel->update($id, $deleteData)) {
            return redirect()->to('admin/activities')->with('success', 'Activity deleted successfully.');
        } else {
            return redirect()->back()->with('error', 'Failed to delete activity.');
        }
    }
}
