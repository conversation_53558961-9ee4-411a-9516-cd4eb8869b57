<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <title><?= isset($title) ? $title . ' - ' : '' ?>PCOLLX Field Portal</title>
    
    <!-- Minimal CSS for low bandwidth -->
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8f9fa;
            color: #333;
            line-height: 1.4;
            -webkit-font-smoothing: antialiased;
            -webkit-tap-highlight-color: transparent;
        }
        
        .container {
            max-width: 100%;
            padding: 0 15px;
            margin: 0 auto;
        }
        
        /* Header */
        .header {
            background: #007bff;
            color: white;
            padding: 12px 0;
            position: sticky;
            top: 0;
            z-index: 100;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .logo {
            font-size: 18px;
            font-weight: 600;
        }
        
        .user-info {
            font-size: 14px;
            text-align: right;
        }
        
        /* Main content */
        .main {
            padding: 20px 0;
            min-height: calc(100vh - 140px);
        }
        
        /* Button styles */
        .btn {
            display: inline-block;
            padding: 12px 20px;
            background: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            border: none;
            font-size: 16px;
            font-weight: 500;
            text-align: center;
            cursor: pointer;
            transition: background 0.2s;
            min-height: 48px;
            line-height: 1.2;
        }
        
        .btn:hover, .btn:focus {
            background: #0056b3;
            color: white;
            text-decoration: none;
        }
        
        .btn-block {
            display: block;
            width: 100%;
            margin-bottom: 15px;
        }
        
        .btn-secondary {
            background: #6c757d;
        }
        
        .btn-secondary:hover {
            background: #545b62;
        }
        
        .btn-success {
            background: #28a745;
        }
        
        .btn-success:hover {
            background: #1e7e34;
        }
        
        .btn-warning {
            background: #ffc107;
            color: #212529;
        }
        
        .btn-warning:hover {
            background: #e0a800;
            color: #212529;
        }
        
        .btn-danger {
            background: #dc3545;
        }
        
        .btn-danger:hover {
            background: #c82333;
        }
        
        /* Grid system */
        .row {
            display: flex;
            flex-wrap: wrap;
            margin: 0 -7.5px;
        }
        
        .col {
            flex: 1;
            padding: 0 7.5px;
            margin-bottom: 15px;
        }
        
        .col-6 {
            flex: 0 0 50%;
            max-width: 50%;
        }
        
        /* Cards */
        .card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .card-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 15px;
            color: #333;
        }
        
        /* Stats */
        .stat-card {
            text-align: center;
            padding: 15px;
        }
        
        .stat-number {
            font-size: 24px;
            font-weight: 700;
            color: #007bff;
            display: block;
        }
        
        .stat-label {
            font-size: 14px;
            color: #666;
            margin-top: 5px;
        }
        
        /* Forms */
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #333;
        }
        
        .form-control {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 6px;
            font-size: 16px;
            background: white;
        }
        
        .form-control:focus {
            outline: none;
            border-color: #007bff;
        }
        
        /* Alerts */
        .alert {
            padding: 12px 15px;
            margin-bottom: 20px;
            border-radius: 6px;
            font-size: 14px;
        }
        
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .alert-danger {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        /* Footer */
        .footer {
            background: #f8f9fa;
            padding: 15px 0;
            text-align: center;
            color: #666;
            font-size: 14px;
            border-top: 1px solid #dee2e6;
        }
        
        /* Responsive */
        @media (max-width: 576px) {
            .container {
                padding: 0 10px;
            }
            
            .header-content {
                flex-direction: column;
                gap: 5px;
            }
            
            .user-info {
                text-align: center;
            }
            
            .col-6 {
                flex: 0 0 100%;
                max-width: 100%;
            }
        }
        
        /* Loading states */
        .loading {
            opacity: 0.6;
            pointer-events: none;
        }
        
        /* Touch improvements */
        .btn, .form-control {
            -webkit-appearance: none;
            -webkit-border-radius: 0;
        }
        
        /* Prevent zoom on input focus */
        @media screen and (max-width: 767px) {
            .form-control {
                font-size: 16px;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">PCOLLX Field</div>
                <?php if (isset($user)): ?>
                <div class="user-info">
                    <div><?= esc($user['name']) ?></div>
                    <div style="font-size: 12px; opacity: 0.9;"><?= esc($user['role']) ?></div>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main">
        <div class="container">
            <?= $this->renderSection('content') ?>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div>&copy; <?= date('Y') ?> PCOLLX Field Portal - ICCC Papua New Guinea</div>
        </div>
    </footer>

    <!-- Minimal JavaScript for basic functionality -->
    <script>
        // Simple form loading states
        document.addEventListener('DOMContentLoaded', function() {
            const forms = document.querySelectorAll('form');
            forms.forEach(form => {
                form.addEventListener('submit', function() {
                    const submitBtn = form.querySelector('button[type="submit"], input[type="submit"]');
                    if (submitBtn) {
                        submitBtn.classList.add('loading');
                        submitBtn.disabled = true;
                        if (submitBtn.textContent) {
                            submitBtn.dataset.originalText = submitBtn.textContent;
                            submitBtn.textContent = 'Loading...';
                        }
                    }
                });
            });
        });

        // Auto-hide alerts after 5 seconds
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(alert => {
                alert.style.transition = 'opacity 0.5s';
                alert.style.opacity = '0';
                setTimeout(() => alert.remove(), 500);
            });
        }, 5000);
    </script>
</body>
</html>
