<?= $this->extend('templates/admin_template') ?>

<?= $this->section('content') ?>
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card card-dark">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h2 class="text-primary-custom mb-2">
                                <i class="bi bi-tags me-2"></i>Goods Brands Management
                            </h2>
                            <p class="text-muted mb-0">
                                Manage goods brands and their categories
                                <?php if ($selected_group): ?>
                                    - Filtered by: <strong><?= esc($selected_group['group_name']) ?></strong>
                                <?php endif; ?>
                            </p>
                        </div>
                        <div>
                            <a href="<?= base_url('admin/goods-brands/new' . ($group_id ? '?group_id=' . $group_id : '')) ?>"
                               class="btn btn-primary">
                                <i class="bi bi-plus-circle me-2"></i>Add New Brand
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filter and Navigation -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card card-dark">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <div class="d-flex align-items-center">
                                <label for="group_filter" class="form-label text-dark me-3 mb-0">Filter by Group:</label>
                                <select class="form-select form-control-dark" id="group_filter" style="width: auto;">
                                    <option value="">All Groups</option>
                                    <?php foreach ($groups as $id => $name): ?>
                                        <option value="<?= $id ?>" <?= $group_id == $id ? 'selected' : '' ?>>
                                            <?= esc($name) ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6 text-end">
                            <a href="<?= base_url('admin/goods-groups') ?>" class="btn btn-outline-secondary">
                                <i class="bi bi-collection me-2"></i>Manage Groups
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Navigation -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card card-dark">
                <div class="card-header">
                    <h6 class="card-title text-light mb-0">
                        <i class="bi bi-compass me-2"></i>Quick Navigation
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row g-2">
                        <div class="col-md-4">
                            <a href="<?= base_url('admin/goods-groups') ?>" class="btn btn-outline-secondary w-100">
                                <i class="bi bi-collection me-2"></i>Manage Groups
                            </a>
                        </div>
                        <div class="col-md-4">
                            <a href="<?= base_url('admin/goods-items') ?>" class="btn btn-outline-warning w-100">
                                <i class="bi bi-box me-2"></i>Manage Items
                            </a>
                        </div>
                        <div class="col-md-4">
                            <a href="<?= base_url('admin/dashboard') ?>" class="btn btn-outline-primary w-100">
                                <i class="bi bi-speedometer2 me-2"></i>Dashboard
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Brands Table -->
    <div class="row">
        <div class="col-12">
            <div class="card card-dark">
                <div class="card-header">
                    <h5 class="card-title text-light mb-0">
                        <i class="bi bi-list me-2"></i>Goods Brands List
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (empty($brands)): ?>
                        <div class="text-center py-5">
                            <i class="bi bi-tags text-muted" style="font-size: 4rem;"></i>
                            <h4 class="text-muted mt-3">No Goods Brands Found</h4>
                            <p class="text-muted">
                                <?php if ($selected_group): ?>
                                    No brands found for "<?= esc($selected_group['group_name']) ?>".
                                <?php else: ?>
                                    Start by adding your first goods brand.
                                <?php endif; ?>
                            </p>
                            <a href="<?= base_url('admin/goods-brands/new' . ($group_id ? '?group_id=' . $group_id : '')) ?>"
                               class="btn btn-primary">
                                <i class="bi bi-plus-circle me-2"></i>Add Goods Brand
                            </a>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-dark">
                                    <tr>
                                        <th>#</th>
                                        <th>Brand Name</th>
                                        <th>Group</th>
                                        <th>Type</th>
                                        <th>Status</th>
                                        <?php if (!$selected_group): ?>
                                            <th>Items Count</th>
                                        <?php else: ?>
                                            <th>Items Count</th>
                                        <?php endif; ?>
                                        <th>Created</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody class="table-light">
                                    <?php foreach ($brands as $index => $brand): ?>
                                        <tr>
                                            <td><?= $index + 1 ?></td>
                                            <td>
                                                <strong class="text-primary-custom"><?= esc($brand['brand_name']) ?></strong>
                                            </td>
                                            <td>
                                                <span class="badge bg-secondary">
                                                    <?= esc($brand['group_name'] ?? 'Unknown') ?>
                                                </span>
                                            </td>
                                            <td>
                                                <?php if ($brand['type'] === 'primary'): ?>
                                                    <span class="badge bg-primary">
                                                        <i class="bi bi-star me-1"></i>Primary
                                                    </span>
                                                <?php else: ?>
                                                    <span class="badge bg-warning text-dark">
                                                        <i class="bi bi-arrow-repeat me-1"></i>Substitute
                                                    </span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if ($brand['status'] === 'active'): ?>
                                                    <span class="badge bg-success">
                                                        <i class="bi bi-check-circle me-1"></i>Active
                                                    </span>
                                                <?php else: ?>
                                                    <span class="badge bg-secondary">
                                                        <i class="bi bi-pause-circle me-1"></i>Inactive
                                                    </span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <span class="badge bg-info">
                                                    <?= $brand['item_count'] ?? 0 ?> items
                                                </span>
                                            </td>
                                            <td>
                                                <small class="text-muted">
                                                    <?= date('M d, Y', strtotime($brand['created_at'])) ?>
                                                </small>
                                            </td>
                                            <td>
                                                <div class="d-flex gap-2">
                                                    <!-- Navigation Button (Primary Action) -->
                                                    <a href="<?= base_url('admin/goods-items?brand_id=' . $brand['id']) ?>"
                                                       class="btn btn-sm btn-primary">
                                                        <i class="bi bi-box me-1"></i>View Items
                                                    </a>

                                                    <!-- Record Modification Buttons -->
                                                    <div class="btn-group" role="group">
                                                        <a href="<?= base_url('admin/goods-brands/' . $brand['id']) ?>"
                                                           class="btn btn-sm btn-outline-info" title="View">
                                                            <i class="bi bi-eye"></i>
                                                        </a>
                                                        <a href="<?= base_url('admin/goods-brands/' . $brand['id'] . '/edit') ?>"
                                                           class="btn btn-sm btn-outline-warning" title="Edit">
                                                            <i class="bi bi-pencil"></i>
                                                        </a>
                                                        <?php if (($brand['item_count'] ?? 0) == 0): ?>
                                                            <form method="post" action="<?= base_url('admin/goods-brands/' . $brand['id'] . '/delete') ?>"
                                                                  class="d-inline" onsubmit="return confirm('Are you sure you want to delete this goods brand?')">
                                                                <button type="submit" class="btn btn-sm btn-outline-danger" title="Delete">
                                                                    <i class="bi bi-trash"></i>
                                                                </button>
                                                            </form>
                                                        <?php else: ?>
                                                            <button type="button" class="btn btn-sm btn-outline-danger"
                                                                    title="Cannot delete - has items" disabled>
                                                                <i class="bi bi-trash"></i>
                                                            </button>
                                                        <?php endif; ?>
                                                    </div>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Group filter functionality
    const groupFilter = document.getElementById('group_filter');
    
    groupFilter.addEventListener('change', function() {
        const selectedGroupId = this.value;
        const currentUrl = new URL(window.location.href);
        
        if (selectedGroupId) {
            currentUrl.searchParams.set('group_id', selectedGroupId);
        } else {
            currentUrl.searchParams.delete('group_id');
        }
        
        window.location.href = currentUrl.toString();
    });
});
</script>
<?= $this->endSection() ?>
